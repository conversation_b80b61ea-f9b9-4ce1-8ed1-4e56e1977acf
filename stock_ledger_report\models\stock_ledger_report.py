# -*- coding: utf-8 -*-
from odoo import models, api, fields
from datetime import datetime


class StockLedgerReport(models.AbstractModel):
    """
    Stock Ledger Report Model
    Generates detailed stock ledger report with product categories,
    voucher details, lot numbers, and movement tracking
    """
    _name = 'report.stock_ledger_report.stock_ledger_template'
    _description = 'Stock Ledger Report'

    @api.model
    def _get_report_values(self, docids, data=None):
        """
        Generate report data for stock ledger
        """
        if not data:
            return {}
            
        form_data = data.get('form_data', {})
        start_date = form_data.get('start_date')
        end_date = form_data.get('end_date')
        warehouse_ids = form_data.get('warehouse_ids', [])
        category_ids = form_data.get('category_ids', [])
        
        # Get stock moves within date range
        domain = [
            ('date', '>=', start_date),
            ('date', '<=', end_date),
            ('state', '=', 'done'),
        ]
        
        if warehouse_ids:
            domain.append(('location_id.warehouse_id', 'in', warehouse_ids))
            
        if category_ids:
            domain.append(('product_id.categ_id', 'in', category_ids))
            
        stock_moves = self.env['stock.move'].search(domain, order='date asc, product_id asc')
        
        # Group data by product category
        category_data = {}
        
        for move in stock_moves:
            category = move.product_id.categ_id
            product = move.product_id
            
            if category.id not in category_data:
                category_data[category.id] = {
                    'category_name': category.name,
                    'products': {},
                    'total_qty_received': 0,
                    'total_qty_issued': 0,
                    'total_amount': 0,
                }
            
            if product.id not in category_data[category.id]['products']:
                category_data[category.id]['products'][product.id] = {
                    'product_name': f"[{product.default_code}] {product.name}" if product.default_code else product.name,
                    'moves': [],
                    'running_balance': 0,
                    'total_received': 0,
                    'total_issued': 0,
                    'total_amount': 0,
                }
            
            # Determine if this is incoming or outgoing
            qty_received = 0
            qty_issued = 0
            
            if move.location_dest_id.usage == 'internal' and move.location_id.usage != 'internal':
                # Incoming move
                qty_received = move.product_uom_qty
            elif move.location_id.usage == 'internal' and move.location_dest_id.usage != 'internal':
                # Outgoing move
                qty_issued = move.product_uom_qty
            elif move.location_id.usage == 'internal' and move.location_dest_id.usage == 'internal':
                # Internal transfer - check if it's incoming or outgoing for selected warehouse
                if warehouse_ids:
                    if move.location_dest_id.warehouse_id.id in warehouse_ids:
                        qty_received = move.product_uom_qty
                    elif move.location_id.warehouse_id.id in warehouse_ids:
                        qty_issued = move.product_uom_qty
            
            # Update running balance
            category_data[category.id]['products'][product.id]['running_balance'] += (qty_received - qty_issued)
            
            # Calculate amount (using standard price or move price)
            unit_cost = move.price_unit or product.standard_price
            amount = (qty_received + qty_issued) * unit_cost
            
            move_data = {
                'date': move.date,
                'voucher_no': move.picking_id.name if move.picking_id else move.reference or '',
                'account': move.location_id.name if qty_issued else move.location_dest_id.name,
                'lot_number': move.lot_ids[0].name if move.lot_ids else '',
                'qty_received': qty_received,
                'qty_issued': qty_issued,
                'qty_balance': category_data[category.id]['products'][product.id]['running_balance'],
                'units': move.product_uom.name,
                'per_kg_cost': unit_cost,
                'amount': amount,
                'rate': unit_cost,
                'remark': move.note or '',
                'units_balance': category_data[category.id]['products'][product.id]['running_balance'],
            }
            
            category_data[category.id]['products'][product.id]['moves'].append(move_data)
            category_data[category.id]['products'][product.id]['total_received'] += qty_received
            category_data[category.id]['products'][product.id]['total_issued'] += qty_issued
            category_data[category.id]['products'][product.id]['total_amount'] += amount
            
            # Update category totals
            category_data[category.id]['total_qty_received'] += qty_received
            category_data[category.id]['total_qty_issued'] += qty_issued
            category_data[category.id]['total_amount'] += amount
        
        # Get warehouse names
        warehouse_names = []
        if warehouse_ids:
            warehouses = self.env['stock.warehouse'].browse(warehouse_ids)
            warehouse_names = [w.name for w in warehouses]
        
        return {
            'doc_ids': docids,
            'doc_model': 'stock.ledger.wizard',
            'docs': self.env['stock.ledger.wizard'].browse(docids),
            'start_date': start_date,
            'end_date': end_date,
            'warehouse_names': ', '.join(warehouse_names) if warehouse_names else 'All Warehouses',
            'category_data': category_data,
            'generated_by': self.env.user.name,
            'generated_date': fields.Datetime.now(),
        }
