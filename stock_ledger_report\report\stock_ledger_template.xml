<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <!-- Stock Ledger Report Template -->
        <template id="stock_ledger_template">
            <t t-call="web.html_container">
                <t t-call="web.internal_layout">
                    <div class="page">
                        <!-- Report Header -->
                        <div class="text-center mb-4">
                            <h2 style="font-weight: bold; margin-bottom: 20px;">
                                STOCK LEDGER FROM <t t-esc="start_date"/> TO <t t-esc="end_date"/>
                            </h2>
                            <h3 style="margin-bottom: 10px;">
                                GODOWN: <t t-esc="warehouse_names"/>
                            </h3>
                        </div>

                        <!-- Report Info Table -->
                        <table class="table table-bordered mb-4" style="width: 100%;">
                            <tr style="background-color: #f8f9fa;">
                                <td style="width: 25%; text-align: center; font-weight: bold; padding: 10px;">
                                    Date Range
                                </td>
                                <td style="width: 25%; text-align: center; font-weight: bold; padding: 10px;">
                                    Warehouse
                                </td>
                                <td style="width: 25%; text-align: center; font-weight: bold; padding: 10px;">
                                    Generated By
                                </td>
                                <td style="width: 25%; text-align: center; font-weight: bold; padding: 10px;">
                                    Generated Date
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: center; padding: 8px;">
                                    <t t-esc="start_date"/> to <t t-esc="end_date"/>
                                </td>
                                <td style="text-align: center; padding: 8px;">
                                    <t t-esc="warehouse_names"/>
                                </td>
                                <td style="text-align: center; padding: 8px;">
                                    <t t-esc="generated_by"/>
                                </td>
                                <td style="text-align: center; padding: 8px;">
                                    <t t-esc="generated_date.strftime('%d/%m/%Y %H:%M')"/>
                                </td>
                            </tr>
                        </table>

                        <!-- Main Data Table -->
                        <t t-foreach="category_data.items()" t-as="category_item">
                            <t t-set="category_id" t-value="category_item[0]"/>
                            <t t-set="category_info" t-value="category_item[1]"/>
                            
                            <!-- Category Header -->
                            <div style="background-color: #e9ecef; padding: 8px; margin-top: 20px; font-weight: bold; border: 1px solid #dee2e6;">
                                PRODUCT CATEGORY: <t t-esc="category_info['category_name']"/>
                            </div>

                            <!-- Table Headers -->
                            <table class="table table-bordered table-sm" style="width: 100%; font-size: 10px;">
                                <thead style="background-color: #f8f9fa;">
                                    <tr>
                                        <th style="text-align: center; padding: 5px; width: 6%;">DATE</th>
                                        <th style="text-align: center; padding: 5px; width: 8%;">VOUCHER NO.</th>
                                        <th style="text-align: center; padding: 5px; width: 8%;">ACCOUNT</th>
                                        <th style="text-align: center; padding: 5px; width: 8%;">LOT NUMBER</th>
                                        <th style="text-align: center; padding: 5px; width: 6%;">QTY REC</th>
                                        <th style="text-align: center; padding: 5px; width: 6%;">QTY ISS</th>
                                        <th style="text-align: center; padding: 5px; width: 6%;">QTY BAL</th>
                                        <th style="text-align: center; padding: 5px; width: 5%;">UNITS</th>
                                        <th style="text-align: center; padding: 5px; width: 7%;">PER KG COST</th>
                                        <th style="text-align: center; padding: 5px; width: 7%;">AMOUNT</th>
                                        <th style="text-align: center; padding: 5px; width: 6%;">UNIT ISS</th>
                                        <th style="text-align: center; padding: 5px; width: 6%;">RATE</th>
                                        <th style="text-align: center; padding: 5px; width: 7%;">AMOUNT</th>
                                        <th style="text-align: center; padding: 5px; width: 8%;">REMARK</th>
                                        <th style="text-align: center; padding: 5px; width: 6%;">UNITS BAL</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Product Data -->
                                    <t t-foreach="category_info['products'].items()" t-as="product_item">
                                        <t t-set="product_id" t-value="product_item[0]"/>
                                        <t t-set="product_info" t-value="product_item[1]"/>
                                        
                                        <!-- Product Moves -->
                                        <t t-foreach="product_info['moves']" t-as="move">
                                            <tr>
                                                <td style="text-align: center; padding: 3px;">
                                                    <t t-esc="move['date'].strftime('%d/%m/%Y') if move['date'] else ''"/>
                                                </td>
                                                <td style="text-align: center; padding: 3px;">
                                                    <t t-esc="move['voucher_no']"/>
                                                </td>
                                                <td style="text-align: left; padding: 3px;">
                                                    <t t-esc="move['account']"/>
                                                </td>
                                                <td style="text-align: center; padding: 3px;">
                                                    <t t-esc="move['lot_number']"/>
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <t t-esc="'{:,.2f}'.format(move['qty_received']) if move['qty_received'] else ''"/>
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <t t-esc="'{:,.2f}'.format(move['qty_issued']) if move['qty_issued'] else ''"/>
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <t t-esc="'{:,.2f}'.format(move['qty_balance'])"/>
                                                </td>
                                                <td style="text-align: center; padding: 3px;">
                                                    <t t-esc="move['units']"/>
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <t t-esc="'{:,.2f}'.format(move['per_kg_cost'])"/>
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <t t-esc="'{:,.2f}'.format(move['amount'])"/>
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <!-- Unit Iss - can be customized based on requirements -->
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <t t-esc="'{:,.2f}'.format(move['rate'])"/>
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <t t-esc="'{:,.2f}'.format(move['amount'])"/>
                                                </td>
                                                <td style="text-align: left; padding: 3px;">
                                                    <t t-esc="move['remark']"/>
                                                </td>
                                                <td style="text-align: right; padding: 3px;">
                                                    <t t-esc="'{:,.2f}'.format(move['units_balance'])"/>
                                                </td>
                                            </tr>
                                        </t>
                                        
                                        <!-- Product Total Row -->
                                        <tr style="background-color: #f8f9fa; font-weight: bold;">
                                            <td style="text-align: left; padding: 5px;" colspan="4">
                                                TOTAL - <t t-esc="product_info['product_name']"/>
                                            </td>
                                            <td style="text-align: right; padding: 5px;">
                                                <t t-esc="'{:,.2f}'.format(product_info['total_received'])"/>
                                            </td>
                                            <td style="text-align: right; padding: 5px;">
                                                <t t-esc="'{:,.2f}'.format(product_info['total_issued'])"/>
                                            </td>
                                            <td colspan="3" style="padding: 5px;"></td>
                                            <td style="text-align: right; padding: 5px;">
                                                <t t-esc="'{:,.2f}'.format(product_info['total_amount'])"/>
                                            </td>
                                            <td colspan="5" style="padding: 5px;"></td>
                                        </tr>
                                    </t>
                                    
                                    <!-- Category Total Row -->
                                    <tr style="background-color: #e9ecef; font-weight: bold;">
                                        <td style="text-align: left; padding: 8px;" colspan="4">
                                            TOTAL <t t-esc="category_info['category_name']"/>
                                        </td>
                                        <td style="text-align: right; padding: 8px;">
                                            <t t-esc="'{:,.2f}'.format(category_info['total_qty_received'])"/>
                                        </td>
                                        <td style="text-align: right; padding: 8px;">
                                            <t t-esc="'{:,.2f}'.format(category_info['total_qty_issued'])"/>
                                        </td>
                                        <td colspan="3" style="padding: 8px;"></td>
                                        <td style="text-align: right; padding: 8px;">
                                            <t t-esc="'{:,.2f}'.format(category_info['total_amount'])"/>
                                        </td>
                                        <td colspan="5" style="padding: 8px;"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </t>
                    </div>
                </t>
            </t>
        </template>

        <!-- PDF Report Action -->
        <record id="pdf_stock_ledger_action" model="ir.actions.report">
            <field name="name">Stock Ledger Report</field>
            <field name="model">stock.ledger.wizard</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock_ledger_report.stock_ledger_template</field>
            <field name="report_file">stock_ledger_report.stock_ledger_template</field>
            <field name="binding_model_id" ref="model_stock_ledger_wizard"/>
            <field name="paperformat_id" ref="base.paperformat_a4"/>
        </record>
    </data>
</odoo>
