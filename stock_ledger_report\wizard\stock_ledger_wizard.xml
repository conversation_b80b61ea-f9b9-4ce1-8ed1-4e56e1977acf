<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <!-- Stock Ledger Wizard Form View -->
        <record id="stock_ledger_wizard_view_form" model="ir.ui.view">
            <field name="name">stock.ledger.wizard.view.form</field>
            <field name="model">stock.ledger.wizard</field>
            <field name="arch" type="xml">
                <form string="Stock Ledger Report">
                    <group string="Report Parameters">
                        <group>
                            <field name="start_date" required="1"/>
                            <field name="warehouse_ids" widget="many2many_tags"/>
                        </group>
                        <group>
                            <field name="end_date" required="1"/>
                            <field name="category_ids" widget="many2many_tags"/>
                        </group>
                    </group>
                    <group string="Company">
                        <field name="company_id" options="{'no_create': True}"/>
                    </group>
                    <footer>
                        <div style="display: flex; justify-content: center; align-items: center; gap: 10px;">
                            <button name="pdf_action" 
                                    string="Generate PDF" 
                                    type="object" 
                                    class="oe_highlight"
                                    icon="fa-file-pdf-o"/>
                            <span style="vertical-align: middle;">or</span>
                            <button name="excel_action" 
                                    string="Export Excel" 
                                    type="object" 
                                    class="oe_highlight"
                                    icon="fa-file-excel-o"/>
                            <span style="margin: 0 5px;">or</span>
                            <button special="cancel" 
                                    string="Cancel" 
                                    type="object"
                                    class="btn-secondary"/>
                        </div>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Stock Ledger Wizard Action -->
        <record id="stock_ledger_wizard_action" model="ir.actions.act_window">
            <field name="name">Stock Ledger Report</field>
            <field name="res_model">stock.ledger.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="stock_ledger_wizard_view_form"/>
        </record>
    </data>
</odoo>
