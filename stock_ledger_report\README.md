# Stock Ledger Report

A comprehensive Odoo module for generating detailed stock ledger reports with product category grouping and movement tracking.

## Features

### 📊 Comprehensive Stock Tracking
- **Detailed Movement Records**: Track all stock movements with complete voucher details
- **Lot Number Tracking**: Include lot/serial numbers in the report
- **Quantity Tracking**: Monitor received, issued, and balance quantities
- **Cost Analysis**: Track per kg costs, rates, and amounts

### 📁 Product Category Organization
- **Category Grouping**: Organize products by categories (e.g., <PERSON><PERSON>, <PERSON><PERSON>)
- **Category Totals**: Automatic calculation of totals per category
- **Flexible Filtering**: Filter by specific product categories

### 🏢 Warehouse Management
- **Multi-warehouse Support**: Generate reports for specific warehouses
- **Warehouse Filtering**: Option to include all warehouses or specific ones
- **Location Tracking**: Track movements between different locations

### 📅 Date Range Flexibility
- **Custom Date Ranges**: Select any start and end date
- **Historical Analysis**: Analyze stock movements over any period
- **Date Validation**: Automatic validation of date ranges

### 📄 Export Options
- **PDF Reports**: Professional PDF reports with proper formatting
- **Excel Export**: Detailed Excel files for further analysis
- **Print-ready Format**: Optimized layouts for printing

## Installation

1. Copy the `stock_ledger_report` folder to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "Stock Ledger Report" module

## Usage

### Accessing the Report
1. Go to **Inventory > Reporting > Stock Ledger Report**
2. Configure the report parameters:
   - **Start Date**: Beginning of the reporting period
   - **End Date**: End of the reporting period
   - **Warehouses**: Select specific warehouses (optional)
   - **Product Categories**: Filter by categories (optional)
   - **Company**: Select the company

### Generating Reports
- Click **Generate PDF** for a formatted PDF report
- Click **Export Excel** for an Excel file with detailed data

## Report Structure

The report includes the following columns:
- **DATE**: Transaction date
- **VOUCHER NO.**: Reference number of the transaction
- **ACCOUNT**: Source/destination location
- **LOT NUMBER**: Lot or serial number
- **QTY REC**: Quantity received
- **QTY ISS**: Quantity issued
- **QTY BAL**: Running quantity balance
- **UNITS**: Unit of measure
- **PER KG COST**: Cost per kilogram
- **AMOUNT**: Total amount
- **UNIT ISS**: Units issued
- **RATE**: Rate per unit
- **REMARK**: Additional notes

## Technical Details

### Dependencies
- `stock`: Core inventory module
- `stock_account`: Stock accounting
- `product`: Product management

### Models
- `stock.ledger.wizard`: Report configuration wizard
- `report.stock_ledger_report.stock_ledger_template`: Report generation model

### Key Files
- `models/stock_ledger_report.py`: Report logic and data processing
- `wizard/stock_ledger_wizard.py`: User interface and Excel generation
- `report/stock_ledger_template.xml`: PDF report template
- `wizard/stock_ledger_wizard.xml`: Wizard form view

## Customization

The module can be customized to:
- Add additional columns
- Modify grouping logic
- Change report formatting
- Add new filtering options
- Integrate with other modules

## Support

For support and customization requests, please contact your Odoo developer or system administrator.

## License

This module is licensed under LGPL-3.
