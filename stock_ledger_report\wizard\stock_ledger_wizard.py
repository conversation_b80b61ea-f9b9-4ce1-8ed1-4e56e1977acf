# -*- coding: utf-8 -*-
from io import BytesIO
import base64
import xlwt
from datetime import datetime
from odoo import fields, models, api
from odoo.exceptions import UserError


class StockLedgerWizard(models.TransientModel):
    """
    Stock Ledger Report Wizard
    Allows users to configure report parameters and generate
    PDF or Excel reports for stock ledger
    """
    _name = 'stock.ledger.wizard'
    _description = "Stock Ledger Report Wizard"

    start_date = fields.Date(
        string='Start Date',
        required=True,
        default=fields.Date.context_today
    )
    end_date = fields.Date(
        string='End Date',
        required=True,
        default=fields.Date.context_today
    )
    warehouse_ids = fields.Many2many(
        'stock.warehouse',
        string='Warehouses',
        help='Leave empty to include all warehouses'
    )
    category_ids = fields.Many2many(
        'product.category',
        string='Product Categories',
        help='Leave empty to include all categories'
    )
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        required=True,
        default=lambda self: self.env.company
    )

    @api.constrains('start_date', 'end_date')
    def _check_dates(self):
        """Validate date range"""
        for record in self:
            if record.start_date > record.end_date:
                raise UserError("Start date cannot be greater than end date.")

    def pdf_action(self):
        """Generate PDF report"""
        if not self.start_date or not self.end_date:
            raise UserError("Please select both start and end dates.")
            
        data = {
            'form_data': {
                'start_date': self.start_date.strftime('%Y-%m-%d'),
                'end_date': self.end_date.strftime('%Y-%m-%d'),
                'warehouse_ids': self.warehouse_ids.ids,
                'category_ids': self.category_ids.ids,
                'company_id': self.company_id.id,
            }
        }
        
        return self.env.ref('stock_ledger_report.pdf_stock_ledger_action').report_action(self, data=data)

    def excel_action(self):
        """Generate Excel report"""
        if not self.start_date or not self.end_date:
            raise UserError("Please select both start and end dates.")
            
        # Get report data
        report_model = self.env['report.stock_ledger_report.stock_ledger_template']
        data = {
            'form_data': {
                'start_date': self.start_date.strftime('%Y-%m-%d'),
                'end_date': self.end_date.strftime('%Y-%m-%d'),
                'warehouse_ids': self.warehouse_ids.ids,
                'category_ids': self.category_ids.ids,
                'company_id': self.company_id.id,
            }
        }
        
        report_data = report_model._get_report_values(self.ids, data)
        
        # Create Excel workbook
        workbook = xlwt.Workbook()
        worksheet = workbook.add_sheet('Stock Ledger Report')
        
        # Define styles
        header_style = xlwt.easyxf('font: bold on; align: horiz center; borders: left thin, right thin, top thin, bottom thin;')
        data_style = xlwt.easyxf('borders: left thin, right thin, top thin, bottom thin;')
        total_style = xlwt.easyxf('font: bold on; borders: left thin, right thin, top thin, bottom thin;')
        
        row = 0
        
        # Report title
        worksheet.write_merge(row, row, 0, 14, f'STOCK LEDGER REPORT FROM {self.start_date} TO {self.end_date}', header_style)
        row += 2
        
        # Company and warehouse info
        worksheet.write(row, 0, f'Company: {self.company_id.name}', data_style)
        worksheet.write(row, 5, f'Warehouse: {report_data.get("warehouse_names", "All")}', data_style)
        row += 2
        
        # Column headers
        headers = [
            'DATE', 'VOUCHER NO.', 'ACCOUNT', 'LOT NUMBER', 'QTY REC', 'QTY ISS', 
            'QTY BAL', 'UNITS', 'PER KG COST', 'AMOUNT', 'UNIT ISS', 'RATE', 
            'AMOUNT', 'REMARK', 'UNITS BAL'
        ]
        
        for col, header in enumerate(headers):
            worksheet.write(row, col, header, header_style)
        row += 1
        
        # Data rows
        category_data = report_data.get('category_data', {})
        
        for category_id, category_info in category_data.items():
            # Category header
            worksheet.write_merge(row, row, 0, 14, f'PRODUCT CATEGORY: {category_info["category_name"]}', header_style)
            row += 1
            
            for product_id, product_info in category_info['products'].items():
                for move in product_info['moves']:
                    worksheet.write(row, 0, move['date'].strftime('%d/%m/%Y') if move['date'] else '', data_style)
                    worksheet.write(row, 1, move['voucher_no'], data_style)
                    worksheet.write(row, 2, move['account'], data_style)
                    worksheet.write(row, 3, move['lot_number'], data_style)
                    worksheet.write(row, 4, move['qty_received'], data_style)
                    worksheet.write(row, 5, move['qty_issued'], data_style)
                    worksheet.write(row, 6, move['qty_balance'], data_style)
                    worksheet.write(row, 7, move['units'], data_style)
                    worksheet.write(row, 8, move['per_kg_cost'], data_style)
                    worksheet.write(row, 9, move['amount'], data_style)
                    worksheet.write(row, 10, '', data_style)  # Unit Iss
                    worksheet.write(row, 11, move['rate'], data_style)
                    worksheet.write(row, 12, move['amount'], data_style)
                    worksheet.write(row, 13, move['remark'], data_style)
                    worksheet.write(row, 14, move['units_balance'], data_style)
                    row += 1
                
                # Product total
                worksheet.write(row, 0, 'TOTAL', total_style)
                worksheet.write(row, 4, product_info['total_received'], total_style)
                worksheet.write(row, 5, product_info['total_issued'], total_style)
                worksheet.write(row, 9, product_info['total_amount'], total_style)
                row += 2
            
            # Category total
            worksheet.write(row, 0, f'TOTAL {category_info["category_name"]}', total_style)
            worksheet.write(row, 4, category_info['total_qty_received'], total_style)
            worksheet.write(row, 5, category_info['total_qty_issued'], total_style)
            worksheet.write(row, 9, category_info['total_amount'], total_style)
            row += 3
        
        # Save to BytesIO
        file_data = BytesIO()
        workbook.save(file_data)
        file_data.seek(0)
        
        # Create attachment
        filename = f'Stock_Ledger_Report_{self.start_date}_{self.end_date}.xls'
        attachment = self.env['ir.attachment'].create({
            'name': filename,
            'type': 'binary',
            'datas': base64.b64encode(file_data.read()),
            'res_model': self._name,
            'res_id': self.id,
            'mimetype': 'application/vnd.ms-excel',
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'new',
        }
